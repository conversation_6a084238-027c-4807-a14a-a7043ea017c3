 public function store(Request $request)
    {
        $user = auth()->user();

        // 1) Ensure user has access
        if (! $user->hasActiveOrTrialSubscription()) {
            return redirect()->back()
                ->with('error', 'You do not have an active subscription or valid trial.');
        }

        // 2) Validate input
        $validated = $request->validate([
            'name'             => 'required|string|max:255',
            'slug'             => 'nullable|string|max:255',
            'color'            => 'nullable|string|max:7',
            'description'      => 'nullable|string',
            'channels_enabled' => 'nullable|array',
            'channels_enabled.*' => 'string',
            'logo'             => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active'        => 'boolean',
        ]);

        // 3) Check agent limit on active plan
        $activeSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->latest()
            ->first();

        if ($activeSubscription && $activeSubscription->plan->agent_limit !== null) {
            $currentAgents = $user->agents()->count();
            if ($currentAgents >= $activeSubscription->plan->agent_limit) {
                return redirect()->route('agents.index')
                    ->with('error', 'You have reached your agent limit for the current plan.');
            }
        }

        // 4) Handle logo upload
        $logoUrl = null;
        if ($request->hasFile('logo')) {
            $path    = $request->file('logo')->store('logos', 'public');
            $logoUrl = Storage::url($path);
        }

        // 5) Create the Agent
        $agent = Agent::create([
            'user_id'          => $user->id,
            'name'             => $validated['name'],
            'slug'             => $validated['slug'] ?? Str::slug($validated['name']),
            'color'            => $validated['color'] ?? null,
            'description'      => $validated['description'] ?? null,
            'channels_enabled' => $validated['channels_enabled'] ?? [],
            'webhook_url'      => '',      // will set below
            'is_active'        => $validated['is_active'] ?? true,
            'logo_url'         => $logoUrl,
        ]);

        // 6) Prepare N8N client and template ID
        $n8nClient = new \App\N8N\N8NClient();
        $planSlug  = strtolower($user->activePlanSlug ?? 'starter');
        $templates = config('n8n.templates');
        $templateId = $templates[$planSlug] ?? $templates['starter'];

        // 7) Fetch the template workflow
        $templateWorkflow = $n8nClient->getWorkflow($templateId);
        // $templateWorkflow = $n8nClient->getWorkflow($templateId);
        if (! $templateWorkflow || isset($templateWorkflow['status']) && $templateWorkflow['status'] === 'error') {
            $message = match($templateWorkflow['reason'] ?? null) {
                'connection_refused' => 'We couldn’t reach the automation engine. Please check your server connection or try again shortly.',
                'request_exception' => 'Something went wrong while loading your workflow. Please try again later.',
                default => 'An unexpected error occurred while preparing your workflow.'
            };
        
            return redirect()->back()
                ->with('error', $message);
        }
        

        // 8) Recursive cleaner to strip UI metadata
        $cleanPayload = function ($data) use (&$cleanPayload) {
            if (is_array($data)) {
                $out = [];
                foreach ($data as $k => $v) {
                    // skip UI‑only keys
                    if (in_array($k, ['__rl','cachedResultUrl','cachedResultName'])) {
                        continue;
                    }
                    $out[$k] = $cleanPayload($v);
                }
                return $out;
            }
            return $data;
        };

        $cleanWorkflow = $cleanPayload($templateWorkflow);

        // 9) Build the new workflow payload
        $payload = [
            'name'        => "{$user->name} - {$agent->name}",
            'nodes'       => $cleanWorkflow['nodes'] ?? [],
            'connections' => $cleanPayload($cleanWorkflow['connections'] ?? []),
            'settings'    => $cleanWorkflow['settings'] ?? [],
        ];

        // 10) Ensure each node.parameters is an object
        foreach ($payload['nodes'] as &$node) {
            $params = $node['parameters'] ?? [];
            $node['parameters'] = is_object($params) ? $params : (object) $params;

            // 11) Generate and assign unique webhook path
            if ($node['type'] === 'n8n-nodes-base.webhook') {
                $path = 'webhook/' . Str::slug($agent->slug) . '-' . $agent->id;
                $node['parameters']->path = $path;
                $agent->update(['webhook_url' => $path]);
            }
        }

        // 12) Create the new workflow in N8N
        try {
            $newWorkflow = $n8nClient->createWorkflow($payload);
        } catch (\Exception $e) {
            return redirect()->route('agents.index')
                ->with('error', 'Workflow duplication failed: ' . $e->getMessage());
        }

        // 13) Confirm and save workflow ID
        if (! isset($newWorkflow['id'])) {
            return redirect()->route('agents.index')
                ->with('error', 'Agent created but workflow duplication failed.');
        }
        $agent->update(['n8n_workflow_id' => $newWorkflow['id']]);

        // 14) Redirect success
        return redirect()->route('agents.index')
            ->with('success', 'Agent and workflow created successfully.');
    }