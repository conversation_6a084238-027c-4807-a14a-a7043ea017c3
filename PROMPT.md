---

# 🧠 Nova Connect – Architecture & Features Guide

**Nova Connect** | SaaS AI Agent Platform | 2025

**🚀 PROJECT STATUS: Foundation Complete - Core Infrastructure In Progress**

---

## 📁 Project Setup Guide

```bash
composer create-project laravel/laravel nova_connect

# Jetstream (React + Inertia)
composer require laravel/jetstream
php artisan jetstream:install inertia
npx laravel-jetstream-react@latest install
npm install && npm run dev

# shadcn/ui setup
npx shadcn@latest init

# Required Packages
composer require spatie/laravel-permission
composer require spatie/laravel-activitylog
composer require mcamara/laravel-localization

# Publish configs
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan vendor:publish --tag=laravel-localization

# DB Migrations & Seeding
php artisan migrate --seed
```

## ✅ Base Stack

Nova Connect is a modular, AI-powered, multi-tenant SaaS platform powered by:

* ✅ **Laravel 12** (Jetstream + Inertia + React)
* ✅ React (TypeScript + TailwindCSS + shadcn/ui)
* ✅ Role-Based Access (Spatie)
* ✅ n8n Workflow Automation (Self-hosted or Cloud)
* ✅ AI Agent Management & Messaging Hub (OpenAI, ChatGPT, Mistral, Claude, Gemini default supported)
* ✅ CRM + IT Help Desk modules
* ✅ Secure Credential Vault
* ✅ Multichannel Integrations (Slack, WhatsApp, Facebook, Web Chat, etc.)

---

## 🧱 Module-Based Frontend Structure

```
resources/js/Pages/
├── Auth/            // Login, Register, Forgot, 2FA
├── Dashboard/       // User-specific dashboards
├── Admin/           // System & user management
├── Agents/          // Create, train, deploy agents
├── Workflows/       // Trigger & view n8n workflows
├── CRM/             // Leads, Plans, Subscriptions
├── Helpdesk/        // Tickets, SLAs, Knowledge Base
├── Messaging/       // Chat logs, channel configs
├── Settings/        // API keys, account, billing, notifications, intergrations, integrate , mtn, airtel, zamtel for mobile money
```

---

## ⚙️ Technology Stack

| Layer             | Tech                                         |
| ----------------- | -------------------------------------------- |
| **Backend**       | Laravel 12 + Jetstream (Inertia + React)     |
| **Frontend**      | React (TypeScript) + TailwindCSS + shadcn/ui |
| **Automation**    | n8n (Self-hosted or Cloud)                   |
| **AI Agents**     | OpenAI / ChatGPT / Mistral / Claude / Gemini |
| **RBAC**          | `spatie/laravel-permission`                  |
| **i18n**          | `mcamara/laravel-localization`               |
| **Storage**       | Encrypted MySQL + Laravel Filesystem         |
| **Notifications** | Email, Slack, Webhook                        |

---

## 🌍 Internationalization

* 🌐 Languages: **English**, with support for more
* 📂 JSON-based translations: `resources/lang/{locale}.json`
* 🧭 Language switcher via dropdown
* 🌍 Admin & UI text fully translatable

---

## 🔐 Role-Based Access Control

| Role     | Scope                              |
| -------- | ---------------------------------- |
| Admin    | Full system management             |
| Customer | AI agents, CRM, helpdesk, messages |

* Middleware guards: `role`, `permission`
* React hooks: `usePermissions`, `useRole`
* Route groups by role

---

## 🧠 AI Agent Management

### Features:

* Agent creation & deletion
* Name, logo, color, description
* Channel mapping (Slack, WhatsApp, Facebook, Web Chat)
* Configurable LLM provider selection: OpenAI, ChatGPT, Mistral, Claude, Gemini
* Upload knowledge files for training
* Custom prompt templates per agent
* Enable memory & context persistence
* Auto-connect to n8n workflows for automation triggers and actions

---

## 🔁 n8n Workflow Automation

### Built-In:

* Assign workflows to agents dynamically
* Launch/stop workflows manually or triggered by agent events
* View execution logs, status, and history
* Monitor failed or paused runs and retry mechanisms
* Bi-directional webhook sync with Laravel agents and external services
* Support for AI-triggered workflows and automated CRM/Help Desk actions

---

## 🌐 Multichannel Messaging

| Channel           | Integration                |
| ----------------- | -------------------------- |
| Slack             | Webhook + Bot Token        |
| Facebook          | Page + Webhook API         |
| WhatsApp Business | Cloud API or Twilio        |
| X (Twitter)       | App + API key integration  |
| Web Chat          | Built-in embeddable widget |

### Features:

* Real-time 2-way chat across channels
* Persistent message logging & searchable archives
* Channel mapping configurable per agent
* OAuth and token-based authentication
* Automatic message retry & error handling

---

## 🛠️ Credential Vault

Secure storage of third-party API keys and secrets, encrypted at rest:

| Credential Type               | Purpose                  |
| ----------------------------- | ------------------------ |
| Slack/Facebook                | Channel authentication   |
| OpenAI/Claude/Mistral/ChatGPT | AI response generation   |
| Custom                        | API/Webhook integrations |

* 🔐 Auto token refresh and expiry tracking
* ✅ Credentials scoped per agent or workflow

---

## 🧩 CRM Module

### Features:

* **Leads**: Capture via chat or embedded forms
* **Plans**: Monthly/yearly, free trials, pricing tiers
* **Add-Ons**: Optional upsells (extra AI bots, language packs)
* **Subscriptions**: Status, usage, expiry tracking
* **Payments**: Transaction logs and invoice management

---

## 🛟 Help Desk Module

### Ticketing System:

* Priority, category, and SLA status tracking
* SLA enforcement and breach alerts per ticket category
* Internal staff assignments and ticket ownership
* Ticket comments, threaded conversations, and file attachments
* AI-powered auto-responses and ticket classification (via ChatGPT/Mistral)

### Knowledge Base:

* Rich text articles with media embedding
* Searchable by keywords and categories
* Linkable from tickets and chatbots for self-service

---

## 📬 Messaging Logs & Alerts

| Item             | Storage                |
| ---------------- | ---------------------- |
| Inbound messages | `agent_messages` table |
| Agent responses  | Logged + searchable    |
| Alerts           | Email, Slack           |

* Filter logs by agent, channel, date, or user for audits and analysis

---

## 📣 Notifications & Alerts

| Event              | Trigger               |
| ------------------ | --------------------- |
| Agent created      | Welcome email         |
| Token expiring     | Email + Slack alerts  |
| SLA breach         | Email alert           |
| Workflow failed    | Webhook + email       |
| New ticket created | Slack DM notification |

> Email tested via **Mailtrap** (development) and **SMTP** (production)

---

## 📊 Dashboards

* **Admin**: Users, agents, messages, system health KPIs
* **Customer**: Subscription status, agent performance stats, workflow logs

> Built with **Recharts** and dynamic KPI widgets for analytics and monitoring

---

## 💰 AI Agent Services – Pricing & Pitch Deck

### Pricing Structure

| Plan         | Price                        | Features                                                                                                                                                             |
| ------------ | ---------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Starter Plan | K600/month                   | Facebook or WhatsApp Bot (choose one), Up to 300 monthly interactions, Basic flows (FAQs), Standard email/chat support                                               |
| Growth Plan  | K1,200/month                 | Integrations on Facebook, WhatsApp & Website, Up to 1,000 monthly interactions, Custom flows, Lead capture, Monthly reports, Priority support                        |
| Pro Plan     | K2,000+/month (Custom Quote) | All platforms integrated, Unlimited interactions, AI-powered learning & intent detection, E-commerce & CRM integrations, Dedicated support + quarterly optimizations |

### Setup Fee & Add-Ons

* One-Time Setup Fee: K500–K1,000 (complexity-based)
* Optional Add-Ons:

  * Extra Languages: K300/language
  * WhatsApp Business API Setup: K600 (one-time)
  * Staff Training Sessions: K250/session
  * Performance-Based Commission: Negotiable (conversion/sales-tied)

### Pitch Deck Highlights

1. Logo + Tagline: **"24/7 AI Agents That Work While You Sleep"**
2. Problem: Businesses lose leads and sales due to delayed responses.
3. Solution: AI agents automate customer interactions on Facebook, WhatsApp, and websites.
4. Features: Instant replies, lead capture, order processing, FAQs, custom conversation flows.
5. Benefits: Saves time, boosts sales, 24/7 engagement, scales customer support.
6. Pricing overview as above.
7. Testimonials or example use cases (add as available).
8. Call to Action: **"Let's automate your business today!"**

---

## 📁 Project Setup

```bash
# Laravel project setup
composer create-project laravel/laravel nova_connect

# Jetstream + Inertia + React
composer require laravel/jetstream
php artisan jetstream:install inertia
npx laravel-jetstream-react@latest install
npm install && npm run dev

# UI library
npx shadcn@latest init

# Core packages
composer require spatie/laravel-permission
composer require mcamara/laravel-localization
composer require spatie/laravel-activitylog # optional

# Publish configs & migrate
php artisan vendor:publish --tag=laravel-localization
php artisan migrate --seed
```

---

## 🧪 Default Test Users

| Role     | Email            | Password | Notes              |
| -------- | ---------------- | -------- | ------------------ |
| Admin    | `<EMAIL>` | password | Full access        |
| Customer | `<EMAIL>`  | password | Agent + CRM access |

---

## 🧩 Dev Guidelines

* ✅ Use **TypeScript** in React frontend
* ✅ TailwindCSS + shadcn/ui for UI components only
* ✅ Use `sonner` for toast notifications
* ✅ Permissions managed via React hooks
* ✅ Use Laravel Policies for DB authorization
* ✅ Secure API keys and credentials storage
* ✅ Modular design, avoid tight coupling

---

## 🔮 Optional Features (Planned/Future)

* 🗓️ Advanced scheduling/calendar integration
* 📲 Mobile Progressive Web App (PWA) for customers
* 💬 In-app live chat widget
* 🧠 ChatGPT, Mistral, Claude RAG (Retrieval Augmented Generation) extensions
* 🔄 Zapier, Make.com integrations for external workflow automation
* 🧾 Custom invoice builder templates
* ☁️ Cloud backup (AWS, Google Cloud)

---

## 📫 Contact

* **Email:** [<EMAIL>](mailto:<EMAIL>)
* **Website:** [www.novaconnect.ai](http://www.novaconnect.ai)
* **Phone:** +260 971234567
* **Location:** Lusaka, Zambia

---

Let me know if you want this exported as PDF, Word, or Notion — or need UI mockups or code scaffolds for any module.

---

